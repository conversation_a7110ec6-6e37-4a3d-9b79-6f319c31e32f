# AI Agent Analysis Companion - Implementation Plan

## 🎯 Project Overview
Implementation of an AI agent analysis companion for the volo-app project using the mcp-use-ts library. The system will provide intelligent code analysis, project insights, and development assistance through Model Context Protocol (MCP) integration.

## 🔍 Research Summary
Based on comprehensive research using MCP tools, the implementation will follow proven patterns from the mcp-use-ts library:

- **Core Library:** mcp-use v1.0.0+ with LangChain.js integration
- **LLM Provider:** OpenAI GPT-4o via @langchain/openai
- **Streaming:** Real-time UI updates using streamEvents() and AI SDK integration
- **Multi-Server Support:** Dynamic MCP server management for different analysis tools

## 🏗️ Architecture Overview

### Core Components
1. **MCP Configuration System** - Environment-specific server configs
2. **AI Agent Service** - Core mcp-use-ts + OpenAI integration
3. **Port Management Extension** - Extended dynamic port allocation
4. **API Layer** - New Hono endpoints for agent communication
5. **Frontend Interface** - React components with streaming support

### Technology Stack Integration
- **Existing:** React + Vite, Hono API, Firebase Auth, PostgreSQL, Tailwind + ShadCN
- **New:** mcp-use, @langchain/openai, MCP servers, streaming infrastructure

## 📋 Implementation Phases

### Phase 1: Infrastructure Setup
**Responsible:** Backend Developer

**Tasks:**
1. **Package Installation**
   ```bash
   # Core dependencies
   pnpm add mcp-use @langchain/openai
   
   # MCP servers for development
   pnpm add -g @modelcontextprotocol/server-filesystem
   pnpm add -g @modelcontextprotocol/server-everything
   ```

2. **Port Management Extension**
   - Extend `scripts/port-manager.js` to include MCP server ports
   - Add ports 5505-5507 for MCP HTTP servers and agent service
   - Update development scripts to handle new port allocation

3. **Environment Configuration**
   - Add `OPENAI_API_KEY` to environment variables
   - Create MCP server configuration files for dev/prod environments

### Phase 2: MCP Configuration System
**Responsible:** Backend Developer

**Files to Create:**
- `scripts/mcp-config.dev.js` - Development MCP server configuration
- `scripts/mcp-config.prod.js` - Production MCP server configuration
- `server/src/config/mcp.ts` - MCP configuration management

**Configuration Pattern:**
```typescript
// Development configuration
export const devMCPConfig = {
  mcpServers: {
    filesystem: {
      command: 'npx',
      args: ['-y', '@modelcontextprotocol/server-filesystem', process.cwd()],
      env: { ALLOWED_EXTENSIONS: '.js,.ts,.tsx,.json,.md,.yml' }
    },
    everything: {
      command: 'npx',
      args: ['-y', '@modelcontextprotocol/server-everything']
    }
  }
}
```

### Phase 3: AI Agent Service Implementation
**Responsible:** Backend Developer

**Files to Create:**
- `server/src/services/ai-agent.ts` - Core AI agent service
- `server/src/types/agent.ts` - TypeScript types for agent operations
- `server/src/utils/mcp-client.ts` - MCP client management utilities

**Service Pattern:**
```typescript
export class AIAgentService {
  private client: MCPClient
  private agent: MCPAgent

  constructor(mcpConfig: any) {
    this.client = MCPClient.fromDict(mcpConfig)
    
    const llm = new ChatOpenAI({
      modelName: 'gpt-4o',
      apiKey: process.env.OPENAI_API_KEY
    })

    this.agent = new MCPAgent({
      llm,
      client: this.client,
      maxSteps: 20,
      useServerManager: true,
      memoryEnabled: true
    })
  }

  async analyzeCode(code: string, analysisType: string) {
    const query = `Analyze this ${analysisType}: ${code}`
    return await this.agent.run(query)
  }

  async streamAnalysis(query: string) {
    return this.agent.streamEvents(query)
  }

  async cleanup() {
    await this.client.closeAllSessions()
  }
}
```

### Phase 4: API Layer Development
**Responsible:** Backend Developer

**Files to Create:**
- `server/src/routes/agent.ts` - Agent API endpoints
- `server/src/middleware/agent-auth.ts` - Agent-specific authentication
- `server/src/controllers/agent.ts` - Agent request handlers

**API Endpoints:**
- `POST /api/v1/agent/analyze` - Code analysis requests
- `GET /api/v1/agent/stream` - SSE streaming endpoint
- `GET /api/v1/agent/tools` - Available MCP tools
- `POST /api/v1/agent/chat` - Interactive chat with agent

### Phase 5: Frontend Components
**Responsible:** Frontend Developer

**Files to Create:**
- `ui/src/components/agent/AgentInterface.tsx` - Main agent interface
- `ui/src/components/agent/StreamingChat.tsx` - Real-time chat component
- `ui/src/components/agent/CodeAnalyzer.tsx` - Code analysis interface
- `ui/src/hooks/useAgentStream.ts` - Custom hook for streaming
- `ui/src/services/agent-api.ts` - API client for agent endpoints

**Component Pattern:**
```tsx
export function AgentInterface() {
  const { completion, input, handleInputChange, handleSubmit } = useCompletion({
    api: '/api/v1/agent/chat',
  })

  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 overflow-y-auto p-4">
        {completion && <div className="prose">{completion}</div>}
      </div>
      <form onSubmit={handleSubmit} className="p-4 border-t">
        <Input
          value={input}
          onChange={handleInputChange}
          placeholder="Ask the AI agent anything..."
        />
      </form>
    </div>
  )
}
```

### Phase 6: Integration & Testing
**Responsible:** Backend + Frontend Developers

**Tasks:**
1. **End-to-End Integration**
   - Connect frontend components with backend API
   - Test complete agent workflow
   - Verify streaming functionality

2. **Development Environment Testing**
   - Test with local MCP servers
   - Verify port management works correctly
   - Test resource cleanup and error handling

3. **Production Deployment Preparation**
   - Configure production MCP servers
   - Test Cloudflare Workers compatibility
   - Verify environment variable handling

## 🔧 Technical Implementation Details

### MCP Server Management
- **Development:** Local MCP servers spawned as child processes
- **Production:** HTTP-based MCP servers or containerized deployments
- **Resource Management:** Proper session cleanup to prevent memory leaks

### Streaming Implementation
- Use `streamEventsToAISDK()` for Vercel AI SDK integration
- Implement SSE endpoints for real-time communication
- Handle connection management and error recovery

### Security Considerations
- API key management through environment variables
- Tool access control using `disallowedTools` configuration
- Input validation and sanitization
- Rate limiting for agent requests

## ✅ Success Criteria
- [ ] AI agent service successfully integrated with mcp-use-ts
- [ ] Real-time streaming interface working in development
- [ ] Proper resource management and cleanup
- [ ] Integration with existing authentication system
- [ ] Production deployment compatibility
- [ ] Comprehensive error handling and logging

## 📚 Next Steps
1. Begin with Phase 1: Infrastructure Setup
2. Install required dependencies and extend port management
3. Create MCP configuration system for both environments
4. Implement core AI agent service with proper error handling
5. Build API layer following existing Hono patterns
6. Create React components with streaming support
7. Test end-to-end integration and prepare for production deployment
